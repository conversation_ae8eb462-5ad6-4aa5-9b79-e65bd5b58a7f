import datetime
from tests.testutils.mock_datetime import MockDatetime
from kbotloadscheduler.loader.basic.preprocess import (
    decode_and_replace_all_links,
    preprocess_items,
    concatenate_content,
    preprocess_sheet,
    parse_datetime
)


# Sample data for testing
sample_sheet_cases = [
    {
        "title": "Case 1",
        "anchor": "cas1",
        "position": 0,
        "univers": [],
        "canal": [],
        "caseTitle": "Title of Case 1",
        "prerequisites": [
            {
                "title": "",
                "markdown": "Niveau d'authentification :  **[niveau 2]"
                            "(https://newbasic.sso.francetelecom.fr/"
                            "public/content/8992 \"lien vers niveau authentification\")** \n\n",
                "richText": "<p>Niveau d&#39;authentification&nbsp;: "
                            "<strong><a href=\"https://newbasic.sso.francetelecom.fr/"
                            "public/content/8992\" rel=\"nofollow\" "
                            "target=\"_blank\" title=\"lien vers niveau authentification\">niveau 2</a></strong></p>",
                "sections": []
            }
        ],
        "keyPoints": [],
        "steps": [],
        "caseContexts": [
           {
                "title": "",
                "markdown": "- Le client détient une offre haut-débit ADSL/VDSL2.\n"
                            "- Il contacte Orange pour modifier son offre.\n",
                "richText": "<ul>\r\n\t<li>Le client d&eacute;tient une offre haut-d&eacute;bit ADSL/VDSL2.</li>\r\n"
                            "\t<li>Il contacte Orange pour modifier son offre.</li>\r\n</ul>",
                "sections": []
            }
        ]
    },
]

sample_sheet_tabs = [
    {
        "title": "Tab 1",
        "richText": "<p>RichText content for Tab 1</p>",
        "markdown": "Markdown content for Tab 1",
        "sections": []
    },
    {
        "title": "Tab 2",
        "richText": "<p>RichText content for Tab 2</p>",
        "markdown": "Markdown content for Tab 2",
        "sections": []
    }
]

sample_sheet = {
    "id": 123,
    "firstTabTitle": "First Tab Title",
    "cases": sample_sheet_cases,
    "tabs": sample_sheet_tabs,
    "shortInfoMarkdown": "Short info in markdown.",
    "generalitiesTitle": "Generalities Title",
    "generalities": [
        {
            "title": "",
            "richText": "<p>Cette procédure est importante</p>",
            "sections": []
        }
    ],
    "content": {
        "id": 321,
        "title": "Sample Sheet Title",
        "abstract": "Abstract of the sheet.",
        "indexableAbstract": "Abstract of the sheet.",
        "tags": [{"name": "tag1"}, {"name": "tag2"}],
        "legalSensitivity": False,
        "statusBa": "00",
    },
    'url_content': 'https://newbasic.sso.francetelecom.fr/public/content/2138',
    'url': 'https://newbasic.sso.francetelecom.fr/public/ProcedureSheet/2057',
    "universe": "universe_example",
    "channel": "channel_example",
    "market": "market_example",
    "customerNeed": "need_example",
    "geographicZone": "zone_example",
    "status": "published",
    "endCommercialValidityDate": "2025-12-31T23:59:59+0000",
    "creationDate": "2024-01-01T10:00:00+0000",
    "modificationDate": "2025-06-01T12:00:00+0000",
    "privateModificationDate": "2025-06-02T16:00:00+0000",
}


class TestPreprocess:
    """
    Test suite for the preprocess.py module.
    """

    def test_decode_and_replace_all_links_relative_links(self):
        """
        Test that relative links in markdown are correctly converted to absolute URLs.
        """
        markdown_text = """
        [CRM GP](/public/content/10068)
        ![](../../contents/Procédurespro/ADCP/acr-media.JPG "picto ACR MEDIA")
        """

        current_url = "https://newbasic.sso.francetelecom.fr/public/content/10068"
        base_url = "https://newbasic.sso.francetelecom.fr"

        expected_output = """
        [CRM GP](https://newbasic.sso.francetelecom.fr/public/content/10068)
        ![](https://newbasic.sso.francetelecom.fr/contents/Proc%C3%A9durespro/ADCP/acr-media.JPG "picto ACR MEDIA")
        """

        result = decode_and_replace_all_links(markdown_text, current_url, base_url)

        print(repr(result))
        print(repr(expected_output), "\n")
        assert result.strip() == expected_output.strip(), "Relative links were not correctly replaced."

    def test_preprocess_items_empty(self):
        """
        Test that preprocessing an empty list returns an empty list.
        """
        items = []
        current_url = "https://example.com"
        expected_output = []

        actual_output = preprocess_items(items, current_url)
        assert actual_output == expected_output, "Preprocessing empty items should return an empty list."

    def test_preprocess_items_nested(self):
        """
        Test preprocessing items with nested sections.
        """
        items = [
            {
                "title": "Section 1",
                "richText": "<p>This is <strong>rich</strong> text for section 1.</p>",
                "sections": [
                    {
                        "title": "Subsection 1.1",
                        "richText": "<p>Rich text for subsection 1.1.</p>",
                        "sections": []
                    }
                ]
            },
            {
                "title": "Section 2",
                "richText": "<p>Rich text for section 2.</p>",
                "sections": []
            }
        ]
        current_url = "https://example.com"

        expected_output = [
            {
                'title': 'Section 1',
                'markdown': 'This is  **rich**  text for section 1.\n\n',
                'richText': '<p>This is <strong>rich</strong> text for section 1.</p>',
                'sections': [
                    {
                        'title': 'Subsection 1.1',
                        'markdown': 'Rich text for subsection 1.1.\n\n',
                        'richText': '<p>Rich text for subsection 1.1.</p>',
                        'sections': []
                    }
                ]},
            {
                'title': 'Section 2',
                'markdown': 'Rich text for section 2.\n\n',
                'richText': '<p>Rich text for section 2.</p>',
                'sections': []
            }
        ]

        actual_output = preprocess_items(items, current_url)

        # Debug: print actual output to see what we get
        print("Actual output:")
        for i, item in enumerate(actual_output):
            print(f"  Item {i}: title={item['title']}, markdown={repr(item['markdown'])}")
            if item['sections']:
                for j, section in enumerate(item['sections']):
                    print(f"    Section {j}: title={section['title']}, markdown={repr(section['markdown'])}")

        assert actual_output == expected_output, "Preprocessing nested items did not produce expected output."

    def test_concatenate_content_dict(self):
        """
        Test concatenation of content from a dictionary.
        """
        content = [{
            "title": "Cas 1 : Modifier une offre haut-débit ADSL / VDSL 2",
            "prerequisites": [
                {
                    "title": "",
                    "markdown": "Niveau d'authentification :  **[niveau 2]"
                                "(https://newbasic.sso.francetelecom.fr/"
                                "public/content/8992 \"lien vers niveau authentification\")** \n\n",
                    "sections": []
                }
            ],
            "keyPoints": [
                {
                    "title": "",
                    "markdown": "####  **Facture** \n\n\n"
                                "Communiquer au client les informations concernant sa première facture.\n\n",
                    "sections": []
                }
            ],
            "steps": [
                {
                    "title": "1. Ouverture de SOFT et identification",
                    "markdown": "Dans les outils concernés suivre les actions suivantes :\n\n\n"
                                "« Traçage modification haut\\-débit »\n\n\n"
                                "| Outil | Point d’Entrée Fonctionnel | SOFT Identification |\n"
                                "| :---: | :---: | :---: |\n"
                                "| CRM GP | Voir la fiche [CRM GP]"
                                "(https://newbasic.sso.francetelecom.fr/public/content/10068)"
                                " et consulter les « pas à pas » présents dans l’onglet de la position de travail "
                                "| Le champ « Téléphone » est pré rempli Sélectionner « modifier l’offre actuelle » |"
                                "\n",
                    "sections": []
                },
                {
                    "title": "2. Configuration offre",
                    "markdown": "#####  **Internet** \n\n\n- SOFT positionne automatiquement le meilleur débit.\n"
                                "- Si le client refuse la montée en débit ou s'il a fait l'objet auparavant "
                                "d'un repli technique, cliquer sur le bouton  pour retourner "
                                "au débit initialement détenu.\n"
                                "- En cas de refus sur la montée en débit et retour à l’initial, "
                                "les options qui avaient été cochées avant dans cette commande "
                                "sont à cocher de nouveau.\n\n#####  **Télévision** \n\n",
                    "sections": [
                        {
                            "title": "Spécificité DROM",
                            "markdown": "La souscription à la télévision numérique réengage "
                                        "le client pendant 12 mois.\n\n",
                            "sections": []
                        }
                    ]
                }

            ],
            "caseContexts": [
                {
                    "title": "",
                    "markdown": "- Le client détient une offre haut-débit ADSL/VDSL2.\n"
                                "- Il contacte Orange pour modifier son offre.\n",
                    "sections": []
                }
            ],
            "title": "Cas 1 : Modifier une offre haut-débit ADSL / VDSL 2"
        }]
        expected_output = '# Cas 1 : Modifier une offre haut-débit ADSL / VDSL 2\n# Pré-requis\n' \
                          'Niveau d\'authentification\xa0:  **[niveau 2]' \
                          '(https://newbasic.sso.francetelecom.fr/public/content/8992 ' \
                          '"lien vers niveau authentification")** \n\n\n' \
                          '# Points-Clés\n####  **Facture** \n\n\n' \
                          'Communiquer au client les informations concernant sa première facture.\n\n\n' \
                          '# Étapes\n' \
                          '## 1. Ouverture de SOFT et identification\n' \
                          'Dans les outils concernés suivre les actions suivantes :\n\n\n' \
                          '« Traçage modification haut\\-débit »\n\n\n' \
                          '| Outil | Point d’Entrée Fonctionnel | SOFT Identification |\n' \
                          '| :---: | :---: | :---: |\n| CRM GP | Voir la fiche [CRM GP]' \
                          '(https://newbasic.sso.francetelecom.fr/public/content/10068) ' \
                          'et consulter les « pas à pas » présents dans l’onglet de la position de travail ' \
                          '| Le champ « Téléphone » est pré rempli Sélectionner « modifier l’offre actuelle » |\n\n' \
                          '## 2. Configuration offre\n#####  **Internet** \n\n\n' \
                          '- SOFT positionne automatiquement le meilleur débit.\n' \
                          '- Si le client refuse la montée en débit ou s\'il a fait l\'objet ' \
                          'auparavant d\'un repli technique, cliquer sur le bouton  ' \
                          'pour retourner au débit initialement détenu.\n' \
                          '- En cas de refus sur la montée en débit et retour à l’initial, les options ' \
                          'qui avaient été cochées avant dans cette commande sont à cocher de nouveau.\n\n' \
                          '#####  **Télévision** \n\n\n## \n### Spécificité DROM\n' \
                          'La souscription à la télévision numérique réengage le client pendant 12 mois.\n\n\n' \
                          '# Contexte\n- Le client détient une offre haut-débit ADSL/VDSL2.\n' \
                          '- Il contacte Orange pour modifier son offre.\n\n'

        actual_output = concatenate_content(content)

        print(repr(actual_output))
        assert actual_output.strip() == expected_output.strip(), \
            "Concatenated content from dict does not match expected output."

    def test_preprocess_sheet_with_cases_and_tabs(self):
        """
        Test preprocessing a sheet that contains cases.
        """
        # sheet = sample_sheet
        # current_url = "https://newbasic.sso.francetelecom.fr/public/content/10068"

        expected_content = '# Sample Sheet Title\n# \n# Case 1\n# Contexte\n' \
                           '- Le client détient une offre haut-débit ADSL/VDSL2.\n' \
                           '- Il contacte Orange pour modifier son offre.\n\n' \
                           '# Pré-requis\nNiveau d\'authentification\xa0:  ' \
                           '**[niveau 2](https://newbasic.sso.francetelecom.fr/public/content/8992 ' \
                           '"lien vers niveau authentification")** \n\n\n\n# \n' \
                           '## Tab 1\nMarkdown content for Tab 1\n## Tab 2\nMarkdown content for Tab 2\n'

        expected_metadata = {
            'channel': 'channel_example',
            'creationDate': '2024-01-01T10:00:00+0000',
            'customerNeed': 'need_example',
            'endCommercialValidityDate': '2025-12-31T23:59:59+0000',
            'fiche_type': 'procedure',
            'geographicZone': 'zone_example',
            'market': 'market_example',
            'modificationDate': '2025-06-02T16:00:00+0000',
            'publicModificationDate': '2025-06-01T12:00:00+0000',
            'privateModificationDate': '2025-06-02T16:00:00+0000',
            'procedure_id': 123,
            'shortInfo': 'Short info in markdown.',
            'status': 'published',
            'legalSensitivity': False,
            'statusBa': '00',
            'structured':
                {
                    'cases': [{
                        'caseContexts': [{
                            'markdown': '- Le client détient une offre haut-débit ADSL/VDSL2.\n'
                                        '- Il contacte Orange pour modifier son offre.\n',
                            'sections': [],
                            'title': ''
                        }],
                        'keyPoints': [],
                        'prerequisites': [{
                            'markdown': 'Niveau '
                                        "d'authentification\xa0"
                                        ':  **[niveau '
                                        '2](https://newbasic.sso.francetelecom.fr/public/content/8992 '
                                        '"lien vers niveau '
                                        'authentification")** \n'
                                        '\n',
                            'sections': [],
                            'title': ''
                        }],
                        'steps': [],
                        'title': 'Title of Case 1'
                    }],
                    'firstTabTitle': 'First Tab Title',
                    'generalities': [{'sections': [], 'title': ''}],
                    'generalitiesTitle': 'Generalities Title',
                    'tabs': [
                        {
                            'markdown': 'Markdown content for Tab 1',
                            'sections': [],
                            'title': 'Tab 1'
                        },
                        {
                            'markdown': 'Markdown content for Tab 2',
                            'sections': [],
                            'title': 'Tab 2'
                        }
                    ]
                },
            'summary': 'Abstract of the sheet.',
            'titre': 'Sample Sheet Title',
            'universe': 'universe_example',
            'url': 'https://newbasic.sso.francetelecom.fr/public/ProcedureSheet/2057',
            'url_content': 'https://newbasic.sso.francetelecom.fr/public/content/2138'
        }

        actual_content, actual_metadata = preprocess_sheet(sample_sheet, "prd")

        assert actual_content.strip() == expected_content.strip(), \
            "Preprocessed content with cases does not match expected output."
        assert actual_metadata == expected_metadata, \
            "Preprocessed metadata with cases does not match expected output."

    def test_parse_datetime_ok(self):
        string_datetime = '2025-06-02T16:00:00+0000'
        assert parse_datetime(string_datetime) == datetime.datetime(2025, 6, 2, 16, 0, 0, 0,
                                                                    tzinfo=datetime.timezone.utc)

    def test_parse_datetime_none(self, mocker):
        MockDatetime(mocker, "kbotloadscheduler.loader.basic.preprocess.datetime", "202409121630")
        assert parse_datetime(None) == datetime.datetime(2024, 9, 12, 0, 0, 0, 0,
                                                         tzinfo=datetime.timezone.utc)
